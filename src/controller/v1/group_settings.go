package v1

import (
	"mi-restful-api/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

type GroupSettings struct {
	TableName string
}

// @Summary グループ設定取得
// @Description グループ設定取得
// @Produce  json
// @Success 200 {object} response.GroupSettingsIndexResp "response"
// @Failure 400 {object} response.GroupSettingsIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.GroupSettingsIndexResp "Unauthorized"
// @Failure 404 {object} response.GroupSettingsIndexResp "Not Found"
// @Failure 500 {object} response.GroupSettingsIndexResp "Internal Server Error"
// @Failure 601 {object} response.GroupSettingsIndexResp "mysql conn error"
// @Failure 602 {object} response.GroupSettingsIndexResp "mysql sql error"
// @Router /web/group/settings [get]
// @Router /app/group/settings [get]
// @Tags GroupSettings
func (rec *GroupSettings) Index(c *gin.Context) {
	var resp response.GroupSettingsIndexResp

	one := 1
	resp.Data.EnableSelfScorePrint = &one
	resp.Data.EnableQuestionnaire = &one
	resp.Data.EnableStartGuide = &one
	resp.Data.CardReaderType = &one

	resp.Msg = "ok"
	resp.Code = 200

	c.JSON(http.StatusOK, resp)
}

// @Summary グループ設定保存
// @Description グループ設定保存
// @ID post-groupsettings-store
// @Accept  json
// @Produce  json
// @Param data body request.GroupSettingsCreateReq true "Request payload"
// @Success 200 {object} response.GroupSettingsCreateResp "response"
// @Failure 400 {object} response.GroupSettingsCreateResp "Bad Request ,param error"
// @Failure 401 {object} response.GroupSettingsCreateResp "Unauthorized"
// @Failure 404 {object} response.GroupSettingsCreateResp "Not Found"
// @Failure 500 {object} response.GroupSettingsCreateResp "Internal Server Error"
// @Failure 601 {object} response.GroupSettingsCreateResp "mysql conn error"
// @Failure 602 {object} response.GroupSettingsCreateResp "mysql sql error"
// @Router /web/group/settings [post]
// @Tags GroupSettings
func (rec *GroupSettings) Store(c *gin.Context) {
	var resp response.GroupSettingsCreateResp

	resp.Msg = "ok"
	resp.Code = 200

	c.JSON(http.StatusOK, resp)
}
