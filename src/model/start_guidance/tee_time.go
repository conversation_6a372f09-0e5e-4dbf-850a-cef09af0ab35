package start_guidance

type PlayerTeeTime struct {
	CourseName   string        `json:"courseName"`
	No           string        `json:"no"`
	Time         string        `json:"time"`
	Status       string        `json:"status"`
	CartNo       string        `json:"cartNo"`
	PlayerName   string        `json:"playerName"`
	OtherPlayers []OtherPlayer `json:"otherPlayers"`
}

type OtherPlayer struct {
	LockerNumber string `json:"lockerNumber"`
	PlayerName   string `json:"playerName"`
	CheckedIn    bool   `json:"checkedIn"`
}

type TeeTimesData struct {
	CurrentTime string          `json:"currentTime"`
	TeeCourse   []TeeCourseData `json:"teeCourse"`
	Information string          `json:"information"`
}

type TeeCourseData struct {
	CourseName string    `json:"courseName"`
	TeeTimes   []TeeTime `json:"teeTimes"`
}

type TeeTime struct {
	No      string   `json:"no"`
	Time    string   `json:"time"`
	Status  string   `json:"status"`
	CartNo  string   `json:"cartNo"`
	Players []Player `json:"players"`
}

type Player struct {
	LockerNumber string `json:"lockerNumber"`
}
