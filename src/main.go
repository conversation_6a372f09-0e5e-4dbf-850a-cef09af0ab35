package main

import (
	"context"
	"log/slog"
	"mi-restful-api/client"
	_ "mi-restful-api/docs" // 実際のプロジェクトのモジュールパスに変更する
	"mi-restful-api/enum"
	"mi-restful-api/logging"
	"mi-restful-api/middlewares"
	"mi-restful-api/utils/dynamo"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	ginadapter "github.com/awslabs/aws-lambda-go-api-proxy/gin"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	sloggin "github.com/samber/slog-gin"

	"mi-restful-api/router"
	"mi-restful-api/utils/table_init"
	"time"
)

var ginLambda *ginadapter.GinLambda

func init() {

	// init logger

	logger := logging.Init()

	// init env
	err := godotenv.Load()
	if err != nil {
		slog.Error("Error loading .env file")
	}

	// init Gin
	r := gin.New()

	// Add the sloggin middleware to all routes.
	// The middleware will log all requests attributes.

	r.Use(sloggin.New(logger))

	r.Use(gin.Recovery())

	//  define router
	router.Api(r)
	// init Gin Lambda adapter
	ginLambda = ginadapter.New(r)
}

func localDeploy() {
	// init Gin
	r := gin.New()

	r.Use(middlewares.Cors())

	// init logger
	logger := logging.Init()

	// init env
	err := godotenv.Load()
	if err != nil {
		slog.Error("Error loading .env file")
	}

	// Add the sloggin middleware to all routes.
	// The middleware will log all requests attributes.

	r.Use(sloggin.New(logger))

	r.Use(gin.Recovery())

	// define router
	router.Api(r)

	// init dynamodb table
	//dynamo.CreateScore()
	//dynamo.CreateComment()
	//dynamo.CreateQuestion()

	// データベースの準備ができているかどうかをチェック
	for true {
		_, err := client.GetDbClient()
		if err != nil {
			slog.Error(err.Error())
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}

	// init mysql
	table_init.InitTable()

	// init local dynamodb
	dynamo.CreateAuditTable()
	dynamo.CreateCompeTable()

	//slog.Info("http://swagger.sharenb.com/swagger/index.html")
	slog.Info("http://localhost:888/swagger/index.html")

	// Gin Lambda init
	appPort := os.Getenv("APP_PORT")
	if appPort == "" {
		appPort = "80"
	}
	r.Run(":" + appPort)
}

// @title mi-restful-api
// @version 1.0
// @description This is a dev server.

// @host localhost:888
// @BasePath /api
func main() {
	deployMode := os.Getenv("DEPLOY_MODE")
	switch deployMode {
	case enum.DeployModeServer:
		fallthrough
	case enum.DeployModeLocal:
		localDeploy()
	case enum.DeployModeLambda:
		//  Lambda 関数を起動する
		lambda.Start(Handler)
	default:
		slog.Error("env DEPLOY_MODE config error")
	}

}

// Lambdaの入口関数
func Handler(ctx context.Context, req events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// If no name is provided in the HTTP request body, throw an error
	return ginLambda.ProxyWithContext(ctx, req)
}
